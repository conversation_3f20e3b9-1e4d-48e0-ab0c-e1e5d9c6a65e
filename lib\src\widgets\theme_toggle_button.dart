import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/theme_provider.dart';
import '../config/colors.dart';
import '../config/theme_helper.dart';
import 'locale_provider.dart';

/// A reusable theme toggle button that demonstrates proper setState logic
class ThemeToggleButton extends StatelessWidget {
  final bool showText;
  final double? iconSize;
  final EdgeInsetsGeometry? padding;

  const ThemeToggleButton({
    super.key,
    this.showText = true,
    this.iconSize,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final isDark = themeProvider.isDarkMode;
        final colors = ThemeHelper.getColors(context);

        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: padding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: colors.backgroundSecondary,
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: AppColors.primaryOrange.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: InkWell(
            onTap: () {
              // Demonstrate the correct setState logic for theme toggling
              debugPrint('ThemeToggleButton: Current theme is ${themeProvider.themeMode.name}');
              debugPrint('ThemeToggleButton: Toggling to ${themeProvider.oppositeThemeMode.name}');
              
              themeProvider.toggleTheme();
              
              debugPrint('ThemeToggleButton: New theme is ${themeProvider.themeMode.name}');
            },
            borderRadius: BorderRadius.circular(25),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: Icon(
                    isDark ? Icons.light_mode : Icons.dark_mode,
                    key: ValueKey(isDark),
                    color: AppColors.primaryOrange,
                    size: iconSize ?? 20,
                  ),
                ),
                if (showText) ...[
                  const SizedBox(width: 8),
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    child: Text(
                      isDark 
                          ? (isArabic ? 'فاتح' : 'Clair')
                          : (isArabic ? 'داكن' : 'Sombre'),
                      key: ValueKey('${isDark}_$isArabic'),
                      style: ThemeHelper.getSubtitleStyle(context).copyWith(
                        color: AppColors.primaryOrange,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}

/// A comprehensive theme control panel for testing
class ThemeControlPanel extends StatelessWidget {
  const ThemeControlPanel({super.key});

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';
    final colors = ThemeHelper.getColorsWithListener(context);

    return Card(
      color: colors.card,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'لوحة تحكم الثيم' : 'Panneau de Contrôle du Thème',
              style: ThemeHelper.getSectionTitleStyle(context),
            ),
            const SizedBox(height: 16),
            
            // Current theme status
            Consumer<ThemeProvider>(
              builder: (context, themeProvider, child) {
                return Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: colors.backgroundSecondary,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isArabic ? 'الحالة الحالية:' : 'État actuel:',
                        style: ThemeHelper.getSubtitleStyle(context),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            themeProvider.isDarkMode ? Icons.dark_mode : Icons.light_mode,
                            color: AppColors.primaryOrange,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${themeProvider.themeMode.name.toUpperCase()} MODE',
                            style: ThemeHelper.getBodyStyle(context).copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.primaryOrange,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        isArabic 
                            ? 'الثيم المقابل: ${themeProvider.oppositeThemeMode.name}'
                            : 'Thème opposé: ${themeProvider.oppositeThemeMode.name}',
                        style: ThemeHelper.getCaptionStyle(context),
                      ),
                    ],
                  ),
                );
              },
            ),
            
            const SizedBox(height: 16),
            
            // Control buttons
            Consumer<ThemeProvider>(
              builder: (context, themeProvider, child) {
                return Column(
                  children: [
                    // Toggle button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          debugPrint('=== THEME TOGGLE TEST ===');
                          debugPrint('Before: ${themeProvider.themeMode.name}');
                          themeProvider.toggleTheme();
                          debugPrint('After: ${themeProvider.themeMode.name}');
                          debugPrint('========================');
                        },
                        style: ThemeHelper.getPrimaryButtonStyle(context),
                        icon: Icon(
                          themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                          color: AppColors.pureWhite,
                        ),
                        label: Text(
                          isArabic ? 'تبديل الثيم' : 'Basculer le Thème',
                          style: ThemeHelper.getButtonTextStyle(context),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Specific theme buttons
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              debugPrint('=== SET LIGHT THEME ===');
                              themeProvider.setLightTheme();
                              debugPrint('Theme set to: ${themeProvider.themeMode.name}');
                              debugPrint('======================');
                            },
                            style: ThemeHelper.getSecondaryButtonStyle(context).copyWith(
                              backgroundColor: WidgetStateProperty.all(
                                themeProvider.isLightMode 
                                    ? AppColors.primaryOrange 
                                    : colors.backgroundSecondary,
                              ),
                            ),
                            child: Text(
                              isArabic ? 'فاتح' : 'Clair',
                              style: TextStyle(
                                color: themeProvider.isLightMode 
                                    ? AppColors.pureWhite 
                                    : colors.textPrimary,
                                fontWeight: themeProvider.isLightMode 
                                    ? FontWeight.bold 
                                    : FontWeight.normal,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              debugPrint('=== SET DARK THEME ===');
                              themeProvider.setDarkTheme();
                              debugPrint('Theme set to: ${themeProvider.themeMode.name}');
                              debugPrint('=====================');
                            },
                            style: ThemeHelper.getSecondaryButtonStyle(context).copyWith(
                              backgroundColor: WidgetStateProperty.all(
                                themeProvider.isDarkMode 
                                    ? AppColors.primaryOrange 
                                    : colors.backgroundSecondary,
                              ),
                            ),
                            child: Text(
                              isArabic ? 'داكن' : 'Sombre',
                              style: TextStyle(
                                color: themeProvider.isDarkMode 
                                    ? AppColors.pureWhite 
                                    : colors.textPrimary,
                                fontWeight: themeProvider.isDarkMode 
                                    ? FontWeight.bold 
                                    : FontWeight.normal,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Reset button
                    SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        onPressed: () {
                          debugPrint('=== RESET TO DEFAULT ===');
                          themeProvider.resetToDefault();
                          debugPrint('Theme reset to: ${themeProvider.themeMode.name}');
                          debugPrint('========================');
                        },
                        child: Text(
                          isArabic ? 'إعادة تعيين للافتراضي' : 'Réinitialiser par défaut',
                          style: ThemeHelper.getBodyStyle(context).copyWith(
                            color: AppColors.primaryOrange,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
