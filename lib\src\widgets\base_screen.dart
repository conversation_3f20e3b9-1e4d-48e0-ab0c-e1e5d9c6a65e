import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/theme_helper.dart';
import 'locale_provider.dart';
import 'notifications_screen.dart';
import '../widgets/dashboard.dart';
import 'settings.dart';
import 'history_screen.dart';

class BaseScreen extends StatefulWidget {
  final Widget child;
  final int currentIndex;

  const BaseScreen({
    super.key,
    required this.child,
    required this.currentIndex,
  });

  @override
  State<BaseScreen> createState() => _BaseScreenState();
}

class _BaseScreenState extends State<BaseScreen> {
  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';
    final colors = ThemeHelper.getColorsWithListener(context);

    return Scaffold(
      backgroundColor: colors.backgroundSecondary,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: colors.backgroundSecondary,
        elevation: 0,
        title: Text(
          isArabic ? 'منصة بناء' : 'Plateforme Bâtir',
          style: ThemeHelper.getSectionTitleStyle(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(
              Icons.notifications,
              color: AppColors.primaryOrange,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const NotificationsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.home),
            label: isArabic ? 'الرئيسية' : 'Accueil',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.settings),
            label: isArabic ? 'الإعدادات' : 'Paramètres',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.history),
            label: isArabic ? 'التاريخ' : 'Historique',
          ),
        ],
        currentIndex: widget.currentIndex,
        selectedItemColor: AppColors.primaryOrange,
        unselectedItemColor: colors.textSecondary,
        backgroundColor: colors.surface,
        onTap: (index) {
          if (index != widget.currentIndex) {
            Widget nextScreen;
            switch (index) {
              case 0:
                nextScreen = const DashboardScreen();
                break;
              case 1:
                nextScreen = const SettingsScreen();
                break;
              case 2:
                nextScreen = const HistoryScreen();
                break;
              default:
                return;
            }
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => nextScreen),
            );
          }
        },
        type: BottomNavigationBarType.fixed,
      ),
    );
  }
}