import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../config/theme_helper.dart';
import '../widgets/locale_provider.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  _SignUpScreenState createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  String? _validateName(String? value, bool isArabic) {
    if (value == null || value.isEmpty) {
      return isArabic ? 'يرجى إدخال الاسم الكامل' : 'Veuillez entrer votre nom complet';
    }
    if (value.length < 3) {
      return isArabic ? 'الاسم قصير' : 'Le nom est trop court';
    }
    return null;
  }

  String? _validateEmail(String? value, bool isArabic) {
    if (value == null || value.isEmpty) {
      return isArabic ? 'يرجى إدخال البريد الإلكتروني' : 'Veuillez entrer votre email';
    }
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    if (!emailRegex.hasMatch(value)) {
      return isArabic ? 'البريد الإلكتروني غير صالح' : 'Email invalide';
    }
    return null;
  }

  String? _validatePhone(String? value, bool isArabic) {
    if (value == null || value.isEmpty) {
      return isArabic ? 'يرجى إدخال رقم الهاتف' : 'Veuillez entrer votre numéro de téléphone';
    }
    final phoneRegex = RegExp(r'^\+?[\d\s-]{8,}$');
    if (!phoneRegex.hasMatch(value)) {
      return isArabic ? 'رقم الهاتف غير صالح' : 'Numéro de téléphone invalide';
    }
    return null;
  }

  String? _validatePassword(String? value, bool isArabic) {
    if (value == null || value.isEmpty) {
      return isArabic ? 'يرجى إدخال كلمة المرور' : 'Veuillez entrer votre mot de passe';
    }
    if (value.length < 8) {
      return isArabic ? 'كلمة المرور قصيرة' : 'Le mot de passe est trop court';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value, bool isArabic) {
    if (value == null || value.isEmpty) {
      return isArabic ? 'يرجى تأكيد كلمة المرور' : 'Veuillez confirmer votre mot de passe';
    }
    if (value != _passwordController.text) {
      return isArabic ? 'كلمات المرور غير متطابقة' : 'Les mots de passe ne correspondent pas';
    }
    return null;
  }

  void _handleSignup() {
    if (_formKey.currentState!.validate()) {
      // Implement signup logic here
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';
    final colors = ThemeHelper.getColorsWithListener(context);

    return Scaffold(
      backgroundColor: colors.backgroundSecondary,
      body: Padding(
        padding: Constants.screenPadding,
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _buildPlatformHeader(context, isArabic),
                const SizedBox(height: Constants.extraLargeSpacing),
                _buildSignupCard(context, isArabic),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlatformHeader(BuildContext context, bool isArabic) => Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            isArabic ? 'منصة بناء' : 'Plateforme Bâtir',
            style: ThemeHelper.getTitleStyle(context),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: Constants.smallSpacing),
          Text(
            isArabic ? 'اهلا بك في منصة بناء' : 'Bienvenue sur la plateforme Bâtir',
            style: ThemeHelper.getSubtitleStyle(context),
            textAlign: TextAlign.center,
          ),
        ],
      );

  Widget _buildSignupCard(BuildContext context, bool isArabic) {
    final colors = ThemeHelper.getColors(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: colors.card,
      child: Padding(
        padding: Constants.cardPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildSignupHeader(context, isArabic),
            const SizedBox(height: Constants.largeSpacing),
            _buildFullNameField(context, isArabic),
            const SizedBox(height: Constants.mediumSpacing),
            _buildEmailField(context, isArabic),
            const SizedBox(height: Constants.mediumSpacing),
            _buildPhoneField(context, isArabic),
            const SizedBox(height: Constants.mediumSpacing),
            _buildPasswordField(context, isArabic),
            const SizedBox(height: Constants.mediumSpacing),
            _buildConfirmPasswordField(context, isArabic),
            const SizedBox(height: Constants.largeSpacing),
            _buildSignupButton(context, isArabic),
            const SizedBox(height: Constants.mediumSpacing),
            _buildLoginLink(context, isArabic),
          ],
        ),
      ),
    );
  }

  Widget _buildSignupHeader(BuildContext context, bool isArabic) => Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            isArabic ? 'إنشاء حساب جديد' : 'Créer un nouveau compte',
            style: ThemeHelper.getSectionTitleStyle(context),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: Constants.smallSpacing),
          Text(
            isArabic
                ? 'أدخل بياناتك لإنشاء حساب في منصة بناء'
                : 'Entrez vos informations pour créer un compte',
            style: ThemeHelper.getSubtitleStyle(context),
            textAlign: TextAlign.center,
          ),
        ],
      );

  Widget _buildFullNameField(BuildContext context, bool isArabic) => TextFormField(
        controller: _fullNameController,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validateName(value, isArabic),
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: isArabic ? 'الاسم الكامل *' : 'Nom complet *',
          hintText: isArabic ? 'محمد أحمد' : 'Jean Dupont',
        ),
      );

  Widget _buildEmailField(BuildContext context, bool isArabic) => TextFormField(
        controller: _emailController,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validateEmail(value, isArabic),
        keyboardType: TextInputType.emailAddress,
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: isArabic ? 'البريد الإلكتروني *' : 'Email *',
          hintText: '<EMAIL>',
        ),
      );

  Widget _buildPhoneField(BuildContext context, bool isArabic) => TextFormField(
        controller: _phoneController,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validatePhone(value, isArabic),
        keyboardType: TextInputType.phone,
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: isArabic ? 'رقم الهاتف *' : 'Numéro de téléphone *',
          hintText: isArabic ? '05xxxxxxxx' : '+33xxxxxxxxx',
        ),
      );

  Widget _buildPasswordField(BuildContext context, bool isArabic) => TextFormField(
        controller: _passwordController,
        obscureText: true,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validatePassword(value, isArabic),
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: isArabic ? 'كلمة المرور *' : 'Mot de passe *',
          hintText: '********',
        ),
      );

  Widget _buildConfirmPasswordField(BuildContext context, bool isArabic) => TextFormField(
        controller: _confirmPasswordController,
        obscureText: true,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validateConfirmPassword(value, isArabic),
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: isArabic ? 'تأكيد كلمة المرور *' : 'Confirmer le mot de passe *',
          hintText: '********',
        ),
      );

  Widget _buildSignupButton(BuildContext context, bool isArabic) => SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _handleSignup,
          style: ThemeHelper.getPrimaryButtonStyle(context),
          child: Text(
            isArabic ? 'إنشاء حساب' : 'Créer un compte',
            style: ThemeHelper.getButtonTextStyle(context),
          ),
        ),
      );

  Widget _buildLoginLink(BuildContext context, bool isArabic) => Align(
        alignment: Alignment.center,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                isArabic ? 'لديك حساب بالفعل؟' : 'Déjà un compte ?',
                style: ThemeHelper.getSubtitleStyle(context),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  isArabic ? 'تسجيل الدخول' : 'Connexion',
                  style: ThemeHelper.getSubtitleStyle(context).copyWith(color: AppColors.primaryOrange),
                ),
              ),
            ],
          ),
        ),
      );
}
