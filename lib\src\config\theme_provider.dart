import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum AppThemeMode { light, dark }

class ThemeProvider extends ChangeNotifier {
  AppThemeMode _themeMode = AppThemeMode.dark;
  bool _isDarkMode = true;
  bool _isInitialized = false;

  AppThemeMode get themeMode => _themeMode;
  bool get isDarkMode => _isDarkMode;
  bool get isInitialized => _isInitialized;

  ThemeProvider() {
    _initializeTheme();
  }

  /// Initialize theme asynchronously and notify listeners when ready
  Future<void> _initializeTheme() async {
    await _loadThemeFromPrefs();
    _updateThemeBasedOnMode();
    _isInitialized = true;
    notifyListeners();
  }

  /// Set theme mode explicitly
  void setThemeMode(AppThemeMode mode) {
    if (_themeMode == mode) return;
    _themeMode = mode;
    _updateThemeBasedOnMode();
    _saveThemeToPrefs();
    notifyListeners();
  }

  /// Toggle between light and dark themes with proper setState logic
  void toggleTheme() {
    // Determine the new theme mode based on current state
    final AppThemeMode newMode;

    if (_themeMode == AppThemeMode.dark) {
      // Current is dark → switch to light
      newMode = AppThemeMode.light;
    } else {
      // Current is light → switch to dark
      newMode = AppThemeMode.dark;
    }

    // Apply the new theme mode
    _themeMode = newMode;
    _updateThemeBasedOnMode();
    _saveThemeToPrefs();
    notifyListeners();
  }

  /// Set to light theme specifically
  void setLightTheme() {
    setThemeMode(AppThemeMode.light);
  }

  /// Set to dark theme specifically
  void setDarkTheme() {
    setThemeMode(AppThemeMode.dark);
  }

  /// Update internal state based on theme mode with proper setState logic
  void _updateThemeBasedOnMode() {
    switch (_themeMode) {
      case AppThemeMode.light:
        _isDarkMode = false;
        break;
      case AppThemeMode.dark:
        _isDarkMode = true;
        break;
    }
  }

  /// Get the opposite theme mode (useful for toggle operations)
  AppThemeMode get oppositeThemeMode {
    return _themeMode == AppThemeMode.dark
        ? AppThemeMode.light
        : AppThemeMode.dark;
  }

  /// Check if current theme is light
  bool get isLightMode => _themeMode == AppThemeMode.light;

  /// Force refresh the theme (useful for debugging or manual refresh)
  void refreshTheme() {
    _updateThemeBasedOnMode();
    notifyListeners();
  }

  /// Reset theme to default (dark mode)
  void resetToDefault() {
    setThemeMode(AppThemeMode.dark);
  }

  /// Load theme preference from SharedPreferences with validation
  Future<void> _loadThemeFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeModeIndex = prefs.getInt('theme_mode') ?? AppThemeMode.dark.index;

      // Validate the index to prevent out-of-bounds errors
      if (themeModeIndex >= 0 && themeModeIndex < AppThemeMode.values.length) {
        _themeMode = AppThemeMode.values[themeModeIndex];
      } else {
        // Invalid index, fallback to default
        _themeMode = AppThemeMode.dark;
        debugPrint('ThemeProvider: Invalid theme index $themeModeIndex, using default dark mode');
      }
    } catch (e) {
      // If there's an error loading preferences, use default (dark mode)
      _themeMode = AppThemeMode.dark;
      debugPrint('ThemeProvider: Error loading theme preference: $e');
    }
  }

  /// Save theme preference to SharedPreferences
  Future<void> _saveThemeToPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('theme_mode', _themeMode.index);
      debugPrint('ThemeProvider: Theme saved - ${_themeMode.name}');
    } catch (e) {
      // Handle error with logging
      debugPrint('ThemeProvider: Error saving theme preference: $e');
    }
  }
}
