import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/theme_helper.dart';
import 'locale_provider.dart';

class PaymentScreen extends StatefulWidget {
  final String requestId;
  final double amount;
  final String requestType;

  const PaymentScreen({
    Key? key,
    required this.requestId,
    required this.amount,
    required this.requestType,
  }) : super(key: key);

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final _cardNumberController = TextEditingController();
  final _cardHolderController = TextEditingController();
  final _expiryDateController = TextEditingController();
  final _cvvController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _cardNumberController.dispose();
    _cardHolderController.dispose();
    _expiryDateController.dispose();
    _cvvController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';
    final colors = ThemeHelper.getColorsWithListener(context);

    return Scaffold(
      backgroundColor: colors.backgroundSecondary,
      appBar: AppBar(
        title: Text(
          isArabic ? 'دفع رسوم الطلب' : 'Paiement des frais',
          style: ThemeHelper.getSectionTitleStyle(context).copyWith(color: AppColors.pureWhite),
        ),
        backgroundColor: AppColors.primaryOrange,
        iconTheme: const IconThemeData(color: AppColors.pureWhite),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              _buildPaymentSummary(isArabic),
              const SizedBox(height: 16),
              _buildPaymentMethod(isArabic),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      _processPayment(context, isArabic);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.success,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    isArabic ? 'إتمام عملية الدفع' : 'Finaliser le paiement',
                    style: ThemeHelper.getButtonTextStyle(context),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentSummary(bool isArabic) {
    return Builder(
      builder: (context) {
        final colors = ThemeHelper.getColors(context);

        return Card(
          elevation: 2,
          color: colors.card,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isArabic ? 'ملخص الطلب' : 'Résumé de la demande',
                  style: ThemeHelper.getSectionTitleStyle(context),
                ),
                const SizedBox(height: 16),
                _buildSummaryRow(
                  isArabic ? 'رقم الطلب:' : 'N° de demande:',
                  widget.requestId,
                  isArabic,
                  context,
                ),
                const SizedBox(height: 8),
                _buildSummaryRow(
                  isArabic ? 'نوع الطلب:' : 'Type de demande:',
                  widget.requestType,
                  isArabic,
                  context,
                ),
                const SizedBox(height: 8),
                _buildSummaryRow(
                  isArabic ? 'إجمالي الرسوم:' : 'Montant total:',
                  '${widget.amount.toStringAsFixed(2)} DH',
                  isArabic,
                  context,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPaymentMethod(bool isArabic) {
    return Builder(
      builder: (context) {
        final colors = ThemeHelper.getColors(context);

        return Card(
          elevation: 2,
          color: colors.card,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isArabic ? 'معلومات الدفع' : 'Informations de paiement',
                  style: ThemeHelper.getSectionTitleStyle(context),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _cardHolderController,
                  decoration: ThemeHelper.getInputDecoration(
                    context,
                    labelText: isArabic ? 'الاسم على البطاقة' : 'Nom sur la carte',
                    prefixIcon: const Icon(Icons.person_outline),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return isArabic ? 'يرجى إدخال الاسم' : 'Veuillez saisir le nom';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _cardNumberController,
                  decoration: ThemeHelper.getInputDecoration(
                    context,
                    labelText: isArabic ? 'رقم البطاقة' : 'Numéro de carte',
                    prefixIcon: const Icon(Icons.credit_card),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return isArabic ? 'يرجى إدخال رقم البطاقة' : 'Veuillez saisir le numéro de carte';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _expiryDateController,
                        decoration: ThemeHelper.getInputDecoration(
                          context,
                          labelText: isArabic ? 'تاريخ الانتهاء' : 'Date d\'expiration',
                          prefixIcon: const Icon(Icons.calendar_today),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return isArabic ? 'يرجى إدخال التاريخ' : 'Veuillez saisir la date';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _cvvController,
                        decoration: ThemeHelper.getInputDecoration(
                          context,
                          labelText: 'CVV',
                          prefixIcon: const Icon(Icons.security),
                        ),
                        keyboardType: TextInputType.number,
                        obscureText: true,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return isArabic ? 'يرجى إدخال CVV' : 'Veuillez saisir le CVV';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSummaryRow(String label, String value, bool isArabic, BuildContext context) {
    final colors = ThemeHelper.getColors(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: ThemeHelper.getSubtitleStyle(context),
        ),
        Text(
          value,
          style: ThemeHelper.getSubtitleStyle(context).copyWith(
            fontWeight: FontWeight.bold,
            color: colors.textPrimary,
          ),
        ),
      ],
    );
  }

  void _processPayment(BuildContext context, bool isArabic) {
    // Simuler un traitement de paiement
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryOrange),
          ),
        );
      },
    );

    // Simuler un délai de traitement
    Future.delayed(const Duration(seconds: 2), () {
      Navigator.pop(context); // Fermer le dialogue de chargement

      // Afficher le message de succès
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isArabic ? 'تمت عملية الدفع بنجاح' : 'Paiement effectué avec succès',
            style: const TextStyle(color: AppColors.pureWhite),
          ),
          backgroundColor: AppColors.success,
          duration: const Duration(seconds: 3),
        ),
      );

      // Retourner à l'écran précédent
      Navigator.pop(context);
    });
  }
}



